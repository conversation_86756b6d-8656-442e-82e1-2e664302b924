# 模板更新系统说明

## 系统概述

这是一个基于PHP的自动模板更新系统，支持从远程服务器自动检查和下载模板更新。

## 核心功能

### 1. 自动版本检查
- 每1分钟自动从 `https://d.ly-y.cn/up/new.txt` 下载最新版本信息
- 手动点击"检查更新"时强制下载最新版本信息
- 比较 `New_version` 和 `Now_version` 判断是否有新版本

### 2. 版本文件格式
```
Version_push->1.0.0-2.7.1
Download->https://d.ly-y.cn/up/模板文件.zip
New_version=2.7.2
Now_version=2.7.1
Update_content=更新内容描述
```

### 3. 下载机制
- 使用 `wget` 命令下载文件，支持进度显示
- 自动检测系统中的 wget 路径
- 支持断点续传和重试机制

## 文件结构

```
/
├── update.php              # 更新页面UI
├── update_handler.php      # 更新逻辑处理
├── config.php             # 配置文件（动态读取版本）
├── get_version.php        # 获取当前版本API
├── version_updater.js     # 前端版本更新器
├── auto_update_version.php # 自动更新脚本
├── cron_update.php        # Cron任务脚本
└── temp/                  # 临时文件目录
    ├── new.txt           # 版本信息文件
    ├── auto_update.log   # 自动更新日志
    ├── cron.log         # Cron任务日志
    └── *.zip            # 下载的更新包
```

## 安装和配置

### 1. 系统要求
- PHP 7.0+
- wget 命令行工具
- ZipArchive 扩展
- 可写的 temp 目录

### 2. 权限设置
```bash
chmod 755 temp/
chmod 644 temp/*
chmod +x cron_update.php
```

### 3. 设置Cron任务（可选）
```bash
# 编辑crontab
crontab -e

# 添加以下行（每分钟执行一次）
* * * * * /usr/bin/php /path/to/your/project/cron_update.php
```

## 使用方法

### 1. 页面访问
访问 `index.php?page=update` 进入更新页面

### 2. 检查更新
- 页面加载时自动检查版本文件
- 点击"检查更新"按钮手动检查
- 系统会自动比较版本号

### 3. 执行更新
- 当有新版本时，点击"开始更新"按钮
- 系统会使用wget下载更新包
- 下载完成后自动解压到temp目录

## API接口

### 1. 检查更新
```
POST update_handler.php
Content-Type: application/x-www-form-urlencoded
Body: action=check_update

Response:
{
    "version": "2.7.2",
    "current_version": "2.7.1", 
    "download_url": "https://...",
    "update_content": "更新内容",
    "version_push": "1.0.0-2.7.1",
    "has_update": true
}
```

### 2. 开始更新
```
GET update_handler.php?action=start_update&url=下载链接
Content-Type: text/event-stream

Response: Server-Sent Events
data: {"progress": "50", "speed": "1.2MB/s", ...}
event: complete
data: {"success": true, "message": "更新完成"}
```

## 日志文件

- `temp/auto_update.log` - 自动更新日志
- `temp/cron.log` - Cron任务日志

## 故障排除

### 1. wget不可用
确保系统已安装wget：
```bash
# Ubuntu/Debian
sudo apt-get install wget

# CentOS/RHEL
sudo yum install wget
```

### 2. 权限问题
确保temp目录可写：
```bash
chmod 755 temp/
chown www-data:www-data temp/
```

### 3. 网络问题
检查服务器是否能访问更新服务器：
```bash
wget -O - https://d.ly-y.cn/up/new.txt
```

## 安全注意事项

1. 确保更新服务器的安全性
2. 验证下载文件的完整性
3. 定期清理temp目录中的旧文件
4. 监控日志文件大小，避免占用过多磁盘空间
