<?php
// 测试更新功能
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "测试更新功能...\n\n";

// 测试检查更新
echo "1. 测试检查更新功能:\n";
$_POST['action'] = 'check_update';
ob_start();
include 'update_handler.php';
$result = ob_get_clean();
echo "结果: " . $result . "\n\n";

// 测试版本文件解析
echo "2. 测试版本文件解析:\n";
if (file_exists('./temp/new.txt')) {
    echo "版本文件存在\n";
    $content = file_get_contents('./temp/new.txt');
    echo "文件内容:\n" . $content . "\n\n";
} else {
    echo "版本文件不存在\n\n";
}

// 测试config.php版本获取
echo "3. 测试config.php版本获取:\n";
include 'config.php';
echo "当前版本: " . $current_version . "\n\n";

echo "测试完成!\n";
?>
