<?php
// 配置文件

/**
 * 获取当前版本信息
 * @return string 当前版本号
 */
function getCurrentVersion() {
    $versionFile = './temp/new.txt';

    // 如果版本文件不存在，返回默认版本
    if (!file_exists($versionFile)) {
        return '2.7.1'; // 默认版本
    }

    $content = file_get_contents($versionFile);
    if ($content === false) {
        return '2.7.1'; // 默认版本
    }

    $lines = explode("\n", trim($content));

    foreach ($lines as $line) {
        $line = trim($line);
        if (strpos($line, 'Now_version=') === 0) {
            return substr($line, strlen('Now_version='));
        }
    }

    return '2.7.1'; // 默认版本
}

// 当前模板版本（从版本文件实时读取）
$current_version = getCurrentVersion();

// 其他配置项可以在此添加
?>