<?php
// 更新处理脚本

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

switch ($action) {
    case 'check_update':
        // 设置响应头
        header('Content-Type: application/json');
        
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        checkUpdate();
        break;
    case 'start_update':
        // 设置SSE响应头
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');
        
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        startUpdate();
        break;
    default:
        header('Content-Type: application/json');
        
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        echo json_encode(['error' => '无效的操作']);
        break;
}

/**
 * 检查更新
 */
function checkUpdate() {
    try {
        // 创建临时目录
        $tempDir = './temp';
        if (!is_dir($tempDir)) {
            if (!mkdir($tempDir, 0755, true)) {
                throw new Exception('无法创建临时目录');
            }
        }

        // 下载最新版本信息文件
        $versionUrl = 'https://d.ly-y.cn/up/new.txt';
        $versionFilePath = $tempDir . '/new.txt';

        // 使用wget下载版本信息文件
        $wgetPath = findWgetPath();
        if (!$wgetPath) {
            throw new Exception('系统未安装wget或wget不可用');
        }

        $escapedUrl = escapeshellarg($versionUrl);
        $escapedFilePath = escapeshellarg($versionFilePath);
        $command = "$wgetPath --timeout=30 --tries=3 --user-agent=\"Template Updater/1.0\" -O $escapedFilePath $escapedUrl 2>&1";

        $output = shell_exec($command);
        $exitCode = 0;
        exec($command, $outputArray, $exitCode);

        if ($exitCode !== 0 || !file_exists($versionFilePath)) {
            throw new Exception('无法下载版本信息文件');
        }

        // 解析版本信息文件
        $versionInfo = parseVersionFile($versionFilePath);

        if (!$versionInfo) {
            throw new Exception('版本信息文件格式错误');
        }

        echo json_encode([
            'version' => $versionInfo['New_version'],
            'current_version' => $versionInfo['Now_version'],
            'download_url' => $versionInfo['Download'],
            'update_content' => $versionInfo['Update_content'],
            'version_push' => $versionInfo['Version_push']
        ]);

    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

/**
 * 开始更新
 */
function startUpdate() {
    try {
        // 获取下载链接
        $downloadUrl = isset($_GET['url']) ? $_GET['url'] : (isset($_POST['url']) ? $_POST['url'] : '');

        if (empty($downloadUrl)) {
            throw new Exception('缺少下载链接');
        }

        // 从URL中提取文件名
        $urlParts = parse_url($downloadUrl);
        $pathInfo = pathinfo($urlParts['path']);
        $fileName = $pathInfo['basename'];

        // 如果无法从URL获取文件名，使用默认名称
        if (empty($fileName) || !preg_match('/\.(zip|rar|tar\.gz)$/i', $fileName)) {
            $fileName = 'template.zip';
        }

        // 创建临时目录
        $tempDir = './temp';
        if (!is_dir($tempDir)) {
            if (!mkdir($tempDir, 0755, true)) {
                throw new Exception('无法创建临时目录');
            }
        }

        // 检查目录是否可写
        if (!is_writable($tempDir)) {
            throw new Exception('临时目录不可写: ' . $tempDir);
        }

        // 下载文件
        $filePath = $tempDir . '/' . $fileName;
        $downloadResult = downloadFileWithWget($downloadUrl, $filePath);

        if (!$downloadResult) {
            throw new Exception('文件下载失败');
        }

        // 解压文件
        $extractResult = extractZip($filePath, $tempDir);

        if (!$extractResult) {
            throw new Exception('文件解压失败');
        }

        // 发送完成事件
        echo "event: complete\n";
        echo "data: {\"success\": true, \"message\": \"更新完成\"}\n\n";

    } catch (Exception $e) {
        echo "data: {\"error\": \"" . $e->getMessage() . "\"}\n\n";
    }

    exit;
}

/**
 * 使用wget下载文件
 * @param string $url 下载链接
 * @param string $filePath 保存路径
 * @return bool 是否成功
 */
function downloadFileWithWget($url, $filePath) {
    // 检查目录是否可写
    $dir = dirname($filePath);
    if (!is_writable($dir)) {
        echo "data: {\"error\": \"目录不可写: " . $dir . "\"}\n\n";
        return false;
    }

    // 检查wget是否可用
    $wgetPath = findWgetPath();
    if (!$wgetPath) {
        echo "data: {\"error\": \"系统未安装wget或wget不可用\"}\n\n";
        return false;
    }

    // 构建wget命令
    $escapedUrl = escapeshellarg($url);
    $escapedFilePath = escapeshellarg($filePath);

    // wget参数说明：
    // --progress=bar:force 显示进度条
    // --timeout=300 超时时间5分钟
    // --tries=3 重试3次
    // --continue 支持断点续传
    // --user-agent 设置用户代理
    $command = "$wgetPath --progress=bar:force --timeout=300 --tries=3 --continue --user-agent=\"Template Updater/1.0\" -O $escapedFilePath $escapedUrl 2>&1";

    echo "data: {\"progress\": \"0\", \"speed\": \"0 B/s\", \"downloaded\": \"0 B\", \"total\": \"未知\"}\n\n";
    ob_flush();
    flush();

    // 执行wget命令
    $descriptorspec = array(
        0 => array("pipe", "r"),  // stdin
        1 => array("pipe", "w"),  // stdout
        2 => array("pipe", "w")   // stderr
    );

    $process = proc_open($command, $descriptorspec, $pipes);

    if (is_resource($process)) {
        // 关闭stdin
        fclose($pipes[0]);

        // 读取输出
        while (!feof($pipes[2])) {
            $line = fgets($pipes[2]);
            if ($line !== false) {
                // 解析wget进度输出
                parseWgetProgress($line);
            }
        }

        fclose($pipes[1]);
        fclose($pipes[2]);

        $returnValue = proc_close($process);

        // 检查文件是否下载成功
        if ($returnValue === 0 && file_exists($filePath) && filesize($filePath) > 0) {
            echo "data: {\"progress\": \"100\", \"speed\": \"0 B/s\", \"downloaded\": \"" . formatBytes(filesize($filePath)) . "\", \"total\": \"" . formatBytes(filesize($filePath)) . "\"}\n\n";
            ob_flush();
            flush();
            return true;
        } else {
            echo "data: {\"error\": \"wget下载失败，返回码: " . $returnValue . "\"}\n\n";
            return false;
        }
    } else {
        echo "data: {\"error\": \"无法启动wget进程\"}\n\n";
        return false;
    }
}

/**
 * 查找wget可执行文件路径
 * @return string|false wget路径或false
 */
function findWgetPath() {
    // 常见的wget路径
    $possiblePaths = [
        '/usr/bin/wget',
        '/bin/wget',
        '/usr/local/bin/wget',
        'wget' // 系统PATH中
    ];

    foreach ($possiblePaths as $path) {
        if ($path === 'wget') {
            // 检查系统PATH中的wget
            $output = shell_exec('which wget 2>/dev/null');
            if (!empty($output)) {
                return trim($output);
            }
        } else {
            // 检查具体路径
            if (file_exists($path) && is_executable($path)) {
                return $path;
            }
        }
    }

    return false;
}

/**
 * 解析wget进度输出
 * @param string $line wget输出行
 */
function parseWgetProgress($line) {
    // wget进度格式示例：
    // 50% [======>                   ] 1,024,000    1.00MB/s  eta 10s
    if (preg_match('/(\d+)%.*?(\d+(?:,\d+)*)\s+(\d+(?:\.\d+)?[KMGT]?B\/s)/', $line, $matches)) {
        $progress = $matches[1];
        $downloaded = str_replace(',', '', $matches[2]);
        $speed = $matches[3];

        echo "data: {\"progress\": \"$progress\", \"speed\": \"$speed\", \"downloaded\": \"" . formatBytes($downloaded) . "\", \"total\": \"未知\"}\n\n";
        ob_flush();
        flush();
    }
}

/**
 * 解析版本信息文件
 * @param string $filePath 版本文件路径
 * @return array|false 解析后的版本信息或false
 */
function parseVersionFile($filePath) {
    if (!file_exists($filePath)) {
        return false;
    }

    $content = file_get_contents($filePath);
    if ($content === false) {
        return false;
    }

    $lines = explode("\n", trim($content));
    $versionInfo = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) {
            continue;
        }

        // 解析 Version_push->1.0.0-2.7.1 格式
        if (strpos($line, 'Version_push->') === 0) {
            $versionInfo['Version_push'] = substr($line, strlen('Version_push->'));
        }
        // 解析 Download->url 格式
        elseif (strpos($line, 'Download->') === 0) {
            $versionInfo['Download'] = substr($line, strlen('Download->'));
        }
        // 解析 New_version=2.7.2 格式
        elseif (strpos($line, 'New_version=') === 0) {
            $versionInfo['New_version'] = substr($line, strlen('New_version='));
        }
        // 解析 Now_version=2.7.1 格式
        elseif (strpos($line, 'Now_version=') === 0) {
            $versionInfo['Now_version'] = substr($line, strlen('Now_version='));
        }
        // 解析 Update_content=内容 格式
        elseif (strpos($line, 'Update_content=') === 0) {
            $content = substr($line, strlen('Update_content='));
            // 解码Unicode转义字符
            $versionInfo['Update_content'] = json_decode('"' . $content . '"');
        }
    }

    // 检查必要字段是否存在
    $requiredFields = ['Version_push', 'Download', 'New_version', 'Now_version', 'Update_content'];
    foreach ($requiredFields as $field) {
        if (!isset($versionInfo[$field])) {
            return false;
        }
    }

    return $versionInfo;
}

/**
 * 格式化字节数
 * @param int $bytes 字节数
 * @param int $precision 精度
 * @return string 格式化后的字符串
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * 解压ZIP文件
 * @param string $zipFile ZIP文件路径
 * @param string $extractPath 解压路径
 * @return bool 是否成功
 */
function extractZip($zipFile, $extractPath) {
    $zip = new ZipArchive;
    
    $openResult = $zip->open($zipFile);
    if ($openResult === true) {
        $extractResult = $zip->extractTo($extractPath);
        $zip->close();
        
        if ($extractResult === true) {
            return true;
        } else {
            // 解压失败
            echo "data: {\"error\": \"文件解压失败: 无法解压文件\"}\n\n";
            exit;
        }
    } else {
        // 打开ZIP文件失败
        $errorMsg = '未知错误';
        switch ($openResult) {
            case ZipArchive::ER_NOZIP:
                $errorMsg = '不是有效的ZIP文件';
                break;
            case ZipArchive::ER_INCONS:
                $errorMsg = 'ZIP文件不一致';
                break;
            case ZipArchive::ER_CRC:
                $errorMsg = 'ZIP文件CRC校验失败';
                break;
            case ZipArchive::ER_OPEN:
                $errorMsg = '无法打开ZIP文件';
                break;
        }
        echo "data: {\"error\": \"文件解压失败: " . $errorMsg . "\"}\n\n";
        exit;
    }
}
?>