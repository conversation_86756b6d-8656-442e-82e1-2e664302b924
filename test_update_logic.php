<?php
/**
 * 测试更新逻辑
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 测试更新逻辑 ===\n\n";

// 包含更新处理函数
require_once 'update_handler.php';

// 1. 测试下载版本文件
echo "1. 测试下载版本文件:\n";
$result = downloadVersionFile();
if ($result) {
    echo "✓ 版本文件下载成功\n";
    
    // 检查文件内容
    $versionFilePath = './temp/new.txt';
    if (file_exists($versionFilePath)) {
        $content = file_get_contents($versionFilePath);
        echo "文件内容:\n" . $content . "\n";
    }
} else {
    echo "✗ 版本文件下载失败\n";
}
echo "\n";

// 2. 测试解析版本文件
echo "2. 测试解析版本文件:\n";
$versionFilePath = './temp/new.txt';
if (file_exists($versionFilePath)) {
    $versionInfo = parseVersionFile($versionFilePath);
    if ($versionInfo) {
        echo "✓ 版本文件解析成功\n";
        echo "Version_push: " . $versionInfo['Version_push'] . "\n";
        echo "Download: " . $versionInfo['Download'] . "\n";
        echo "New_version: " . $versionInfo['New_version'] . "\n";
        echo "Now_version: " . $versionInfo['Now_version'] . "\n";
        echo "Update_content: " . $versionInfo['Update_content'] . "\n";
        
        // 检查是否有更新
        $hasUpdate = ($versionInfo['New_version'] !== $versionInfo['Now_version']);
        echo "有新版本: " . ($hasUpdate ? '是' : '否') . "\n";
    } else {
        echo "✗ 版本文件解析失败\n";
    }
} else {
    echo "✗ 版本文件不存在\n";
}
echo "\n";

// 3. 测试检查更新API
echo "3. 测试检查更新API:\n";
$_POST['action'] = 'check_update';
ob_start();
checkUpdate();
$apiResult = ob_get_clean();
echo "API响应: " . $apiResult . "\n";

// 解析JSON响应
$apiData = json_decode($apiResult, true);
if ($apiData) {
    if (isset($apiData['error'])) {
        echo "✗ API返回错误: " . $apiData['error'] . "\n";
    } else {
        echo "✓ API调用成功\n";
        echo "最新版本: " . $apiData['version'] . "\n";
        echo "当前版本: " . $apiData['current_version'] . "\n";
        echo "有更新: " . ($apiData['has_update'] ? '是' : '否') . "\n";
    }
} else {
    echo "✗ API响应格式错误\n";
}
echo "\n";

// 4. 测试wget路径
echo "4. 测试wget可用性:\n";
$wgetPath = findWgetPath();
if ($wgetPath) {
    echo "✓ wget可用: " . $wgetPath . "\n";
} else {
    echo "✗ wget不可用\n";
}
echo "\n";

echo "=== 测试完成 ===\n";
?>
