<?php
/**
 * 创建测试用的ZIP文件
 */

if (!class_exists('ZipArchive')) {
    die('ZipArchive扩展不可用');
}

// 创建一个测试ZIP文件
$zip = new ZipArchive();
$zipFileName = './temp/test_template.zip';

// 确保temp目录存在
if (!is_dir('./temp')) {
    mkdir('./temp', 0755, true);
}

if ($zip->open($zipFileName, ZipArchive::CREATE) === TRUE) {
    
    // 添加一些测试文件
    $zip->addFromString('index.php', '<?php echo "更新后的首页"; ?>');
    $zip->addFromString('config.php', '<?php $version = "2.7.2"; ?>');
    $zip->addFromString('css/style.css', 'body { background: #f0f0f0; }');
    $zip->addFromString('js/app.js', 'console.log("更新后的JS文件");');
    $zip->addFromString('templates/header.php', '<h1>更新后的头部模板</h1>');
    $zip->addFromString('README.md', '# 测试模板更新包\n\n这是一个测试用的更新包。');
    
    $zip->close();
    
    echo "测试ZIP文件创建成功: $zipFileName\n";
    echo "文件大小: " . formatBytes(filesize($zipFileName)) . "\n";
    
    // 列出ZIP文件内容
    $zip = new ZipArchive();
    if ($zip->open($zipFileName) === TRUE) {
        echo "\nZIP文件内容:\n";
        for ($i = 0; $i < $zip->numFiles; $i++) {
            $fileInfo = $zip->statIndex($i);
            echo "- " . $fileInfo['name'] . " (" . formatBytes($fileInfo['size']) . ")\n";
        }
        $zip->close();
    }
    
} else {
    echo "无法创建ZIP文件\n";
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>
