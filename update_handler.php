<?php
// 更新处理脚本

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

switch ($action) {
    case 'check_update':
        // 设置响应头
        header('Content-Type: application/json');
        
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        checkUpdate();
        break;
    case 'start_update':
        // 设置SSE响应头
        header('Content-Type: text/event-stream; charset=utf-8');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type');

        // 禁用输出缓冲
        while (ob_get_level()) {
            ob_end_clean();
        }

        // 设置无限执行时间
        set_time_limit(0);
        ignore_user_abort(false);
        
        startUpdate();
        break;
    default:
        header('Content-Type: application/json');
        
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        echo json_encode(['error' => '无效的操作']);
        break;
}

/**
 * 检查更新
 */
function checkUpdate() {
    try {
        // 创建临时目录
        $tempDir = './temp';
        if (!is_dir($tempDir)) {
            if (!mkdir($tempDir, 0755, true)) {
                throw new Exception('无法创建临时目录');
            }
        }

        // 强制下载最新版本信息文件（删除旧文件，重新下载）
        $versionFilePath = $tempDir . '/new.txt';
        if (file_exists($versionFilePath)) {
            unlink($versionFilePath); // 删除旧文件
        }

        $downloadResult = downloadVersionFileWithDetails();
        if (!$downloadResult['success']) {
            throw new Exception('无法下载版本信息文件: ' . $downloadResult['error']);
        }

        // 解析版本信息文件
        $versionFilePath = $tempDir . '/new.txt';
        $versionInfo = parseVersionFile($versionFilePath);

        if (!$versionInfo) {
            throw new Exception('版本信息文件格式错误');
        }

        // 判断是否有新版本（New_version 与 Now_version 不同）
        $hasUpdate = ($versionInfo['New_version'] !== $versionInfo['Now_version']);

        echo json_encode([
            'version' => $versionInfo['New_version'],
            'current_version' => $versionInfo['Now_version'],
            'download_url' => $versionInfo['Download'],
            'update_content' => $versionInfo['Update_content'],
            'version_push' => $versionInfo['Version_push'],
            'has_update' => $hasUpdate
        ]);

    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

/**
 * 开始更新
 */
function startUpdate() {
    try {
        // 发送开始事件
        echo "data: {\"progress\": \"0\", \"message\": \"开始下载...\"}\n\n";
        @ob_flush();
        @flush();

        // 获取下载链接
        $downloadUrl = isset($_GET['url']) ? $_GET['url'] : (isset($_POST['url']) ? $_POST['url'] : '');

        if (empty($downloadUrl)) {
            throw new Exception('缺少下载链接');
        }

        // 解码URL
        $downloadUrl = urldecode($downloadUrl);

        // 验证URL格式
        if (!filter_var($downloadUrl, FILTER_VALIDATE_URL)) {
            throw new Exception('无效的下载链接: ' . $downloadUrl);
        }

        echo "data: {\"progress\": \"5\", \"message\": \"准备下载: " . basename($downloadUrl) . "\"}\n\n";
        @ob_flush();
        @flush();

        // 从URL中提取文件名
        $urlParts = parse_url($downloadUrl);
        $pathInfo = pathinfo($urlParts['path']);
        $fileName = $pathInfo['basename'];

        // 如果无法从URL获取文件名，使用默认名称
        if (empty($fileName) || !preg_match('/\.(zip|rar|tar\.gz)$/i', $fileName)) {
            $fileName = 'template.zip';
        }

        // 创建临时目录
        $tempDir = './temp';
        if (!is_dir($tempDir)) {
            if (!mkdir($tempDir, 0755, true)) {
                throw new Exception('无法创建临时目录');
            }
        }

        // 检查目录是否可写
        if (!is_writable($tempDir)) {
            throw new Exception('临时目录不可写: ' . $tempDir);
        }

        echo "data: {\"progress\": \"10\", \"message\": \"开始下载文件...\"}\n\n";
        @ob_flush();
        @flush();

        // 下载文件（使用多种方式尝试）
        $filePath = $tempDir . '/' . $fileName;
        $downloadResult = downloadLargeFile($downloadUrl, $filePath);

        if (!$downloadResult) {
            throw new Exception('文件下载失败');
        }

        echo "data: {\"progress\": \"100\", \"message\": \"下载完成\"}\n\n";
        @ob_flush();
        @flush();

        // 发送完成事件
        echo "event: complete\n";
        echo "data: {\"success\": true, \"message\": \"文件下载完成，保存在: $filePath\"}\n\n";

    } catch (Exception $e) {
        echo "event: error\n";
        echo "data: {\"error\": \"" . $e->getMessage() . "\"}\n\n";
        @ob_flush();
        @flush();
    }

    exit;
}



/**
 * 下载大文件（多种方式尝试，支持进度显示）
 * @param string $url 下载链接
 * @param string $filePath 保存路径
 * @return bool 是否成功
 */
function downloadLargeFile($url, $filePath) {
    // 尝试多种下载方式
    $downloadMethods = [
        'curl_with_progress',
        'wget_with_progress',
        'curl_simple',
        'file_get_contents'
    ];

    foreach ($downloadMethods as $method) {
        if (downloadLargeFileWithMethod($url, $filePath, $method)) {
            return true;
        }
    }

    return false;
}

/**
 * 使用指定方法下载大文件
 * @param string $url 下载链接
 * @param string $filePath 保存路径
 * @param string $method 下载方法
 * @return bool 是否成功
 */
function downloadLargeFileWithMethod($url, $filePath, $method) {
    try {
        switch ($method) {
            case 'curl_with_progress':
                return downloadWithCurlProgress($url, $filePath);
            case 'wget_with_progress':
                return downloadWithWgetProgress($url, $filePath);
            case 'curl_simple':
                return downloadWithCurlSimple($url, $filePath);
            case 'file_get_contents':
                return downloadWithFileGetContentsLarge($url, $filePath);
            default:
                return false;
        }
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 使用cURL下载大文件（带进度）
 */
function downloadWithCurlProgress($url, $filePath) {
    if (!function_exists('curl_init')) {
        return false;
    }

    $fp = @fopen($filePath, 'w+');
    if (!$fp) {
        return false;
    }

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_FILE, $fp);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 300);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Template Updater/1.0');
    curl_setopt($ch, CURLOPT_NOPROGRESS, false);
    curl_setopt($ch, CURLOPT_PROGRESSFUNCTION, function($resource, $downloadSize, $downloaded, $uploadSize, $uploaded) {
        if ($downloadSize > 0) {
            $progress = round(($downloaded / $downloadSize) * 100, 2);
            $speed = curl_getinfo($resource, CURLINFO_SPEED_DOWNLOAD);
            $speedFormatted = formatBytes($speed) . '/s';

            echo "data: {\"progress\": \"$progress\", \"speed\": \"$speedFormatted\", \"downloaded\": \"" . formatBytes($downloaded) . "\", \"total\": \"" . formatBytes($downloadSize) . "\"}\n\n";
            @ob_flush();
            @flush();
        }
    });

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);

    curl_close($ch);
    fclose($fp);

    return ($result !== false && $httpCode === 200 && empty($error) && file_exists($filePath) && filesize($filePath) > 0);
}

/**
 * 使用wget下载大文件（带进度）
 */
function downloadWithWgetProgress($url, $filePath) {
    $wgetPath = findWgetPath();
    if (!$wgetPath) {
        return false;
    }

    $escapedUrl = escapeshellarg($url);
    $escapedFilePath = escapeshellarg($filePath);
    $command = "$wgetPath --progress=bar:force --timeout=300 --tries=3 --continue --user-agent=\"Template Updater/1.0\" -O $escapedFilePath $escapedUrl 2>&1";

    echo "data: {\"progress\": \"0\", \"speed\": \"0 B/s\", \"downloaded\": \"0 B\", \"total\": \"未知\"}\n\n";
    @ob_flush();
    @flush();

    $descriptorspec = array(
        0 => array("pipe", "r"),
        1 => array("pipe", "w"),
        2 => array("pipe", "w")
    );

    $process = proc_open($command, $descriptorspec, $pipes);

    if (is_resource($process)) {
        fclose($pipes[0]);

        while (!feof($pipes[2])) {
            $line = fgets($pipes[2]);
            if ($line !== false) {
                parseWgetProgress($line);
            }
        }

        fclose($pipes[1]);
        fclose($pipes[2]);

        $returnValue = proc_close($process);

        if ($returnValue === 0 && file_exists($filePath) && filesize($filePath) > 0) {
            echo "data: {\"progress\": \"100\", \"speed\": \"0 B/s\", \"downloaded\": \"" . formatBytes(filesize($filePath)) . "\", \"total\": \"" . formatBytes(filesize($filePath)) . "\"}\n\n";
            @ob_flush();
            @flush();
            return true;
        }
    }

    return false;
}

/**
 * 使用cURL简单下载
 */
function downloadWithCurlSimple($url, $filePath) {
    if (!function_exists('curl_init')) {
        return false;
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 300);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Template Updater/1.0');

    $data = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($data !== false && $httpCode === 200 && empty($error)) {
        return file_put_contents($filePath, $data) !== false;
    }

    return false;
}

/**
 * 使用file_get_contents下载大文件
 */
function downloadWithFileGetContentsLarge($url, $filePath) {
    if (!ini_get('allow_url_fopen')) {
        return false;
    }

    $context = stream_context_create([
        'http' => [
            'timeout' => 300,
            'user_agent' => 'Template Updater/1.0',
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);

    $data = @file_get_contents($url, false, $context);

    if ($data !== false && !empty($data)) {
        return file_put_contents($filePath, $data) !== false;
    }

    return false;
}

/**
 * 使用wget下载文件
 * @param string $url 下载链接
 * @param string $filePath 保存路径
 * @return bool 是否成功
 */
function downloadFileWithWget($url, $filePath) {
    // 检查目录是否可写
    $dir = dirname($filePath);
    if (!is_writable($dir)) {
        echo "data: {\"error\": \"目录不可写: " . $dir . "\"}\n\n";
        return false;
    }

    // 检查wget是否可用
    $wgetPath = findWgetPath();
    if (!$wgetPath) {
        echo "data: {\"error\": \"系统未安装wget或wget不可用\"}\n\n";
        return false;
    }

    // 构建wget命令
    $escapedUrl = escapeshellarg($url);
    $escapedFilePath = escapeshellarg($filePath);

    // wget参数说明：
    // --progress=bar:force 显示进度条
    // --timeout=300 超时时间5分钟
    // --tries=3 重试3次
    // --continue 支持断点续传
    // --user-agent 设置用户代理
    $command = "$wgetPath --progress=bar:force --timeout=300 --tries=3 --continue --user-agent=\"Template Updater/1.0\" -O $escapedFilePath $escapedUrl 2>&1";

    echo "data: {\"progress\": \"0\", \"speed\": \"0 B/s\", \"downloaded\": \"0 B\", \"total\": \"未知\"}\n\n";
    ob_flush();
    flush();

    // 执行wget命令
    $descriptorspec = array(
        0 => array("pipe", "r"),  // stdin
        1 => array("pipe", "w"),  // stdout
        2 => array("pipe", "w")   // stderr
    );

    $process = proc_open($command, $descriptorspec, $pipes);

    if (is_resource($process)) {
        // 关闭stdin
        fclose($pipes[0]);

        // 读取输出
        while (!feof($pipes[2])) {
            $line = fgets($pipes[2]);
            if ($line !== false) {
                // 解析wget进度输出
                parseWgetProgress($line);
            }
        }

        fclose($pipes[1]);
        fclose($pipes[2]);

        $returnValue = proc_close($process);

        // 检查文件是否下载成功
        if ($returnValue === 0 && file_exists($filePath) && filesize($filePath) > 0) {
            echo "data: {\"progress\": \"100\", \"speed\": \"0 B/s\", \"downloaded\": \"" . formatBytes(filesize($filePath)) . "\", \"total\": \"" . formatBytes(filesize($filePath)) . "\"}\n\n";
            ob_flush();
            flush();
            return true;
        } else {
            echo "data: {\"error\": \"wget下载失败，返回码: " . $returnValue . "\"}\n\n";
            return false;
        }
    } else {
        echo "data: {\"error\": \"无法启动wget进程\"}\n\n";
        return false;
    }
}

/**
 * 查找wget可执行文件路径
 * @return string|false wget路径或false
 */
function findWgetPath() {
    // 常见的wget路径
    $possiblePaths = [
        '/usr/bin/wget',
        '/bin/wget',
        '/usr/local/bin/wget',
        'wget' // 系统PATH中
    ];

    foreach ($possiblePaths as $path) {
        if ($path === 'wget') {
            // 检查系统PATH中的wget
            $output = shell_exec('which wget 2>/dev/null');
            if (!empty($output)) {
                return trim($output);
            }
        } else {
            // 检查具体路径
            if (file_exists($path) && is_executable($path)) {
                return $path;
            }
        }
    }

    return false;
}

/**
 * 解析wget进度输出
 * @param string $line wget输出行
 */
function parseWgetProgress($line) {
    // wget进度格式示例：
    // 50% [======>                   ] 1,024,000    1.00MB/s  eta 10s
    if (preg_match('/(\d+)%.*?(\d+(?:,\d+)*)\s+(\d+(?:\.\d+)?[KMGT]?B\/s)/', $line, $matches)) {
        $progress = $matches[1];
        $downloaded = str_replace(',', '', $matches[2]);
        $speed = $matches[3];

        echo "data: {\"progress\": \"$progress\", \"speed\": \"$speed\", \"downloaded\": \"" . formatBytes($downloaded) . "\", \"total\": \"未知\"}\n\n";
        ob_flush();
        flush();
    }
}

/**
 * 下载版本信息文件（带详细错误信息）
 * @return array 包含success和error字段的结果
 */
function downloadVersionFileWithDetails() {
    $versionUrl = 'https://d.ly-y.cn/up/new.txt';
    $tempDir = './temp';
    $versionFilePath = $tempDir . '/new.txt';

    // 确保临时目录存在
    if (!is_dir($tempDir)) {
        if (!mkdir($tempDir, 0755, true)) {
            return ['success' => false, 'error' => '无法创建临时目录'];
        }
    }

    // 尝试多种下载方式
    $downloadMethods = [
        'curl' => 'cURL下载',
        'file_get_contents' => 'file_get_contents下载',
        'wget' => 'wget下载',
        'fopen' => 'fopen流下载'
    ];

    $errors = [];
    foreach ($downloadMethods as $method => $description) {
        try {
            $result = downloadWithMethodDetailed($versionUrl, $versionFilePath, $method);
            if ($result['success']) {
                // 验证下载的文件内容
                if (file_exists($versionFilePath) && filesize($versionFilePath) > 0) {
                    $content = file_get_contents($versionFilePath);
                    if (!empty($content) && strpos($content, 'New_version=') !== false) {
                        return ['success' => true, 'method' => $description];
                    } else {
                        $errors[] = "$description: 文件内容格式错误";
                    }
                } else {
                    $errors[] = "$description: 文件不存在或为空";
                }
            } else {
                $errors[] = "$description: " . $result['error'];
            }
        } catch (Exception $e) {
            $errors[] = "$description: " . $e->getMessage();
        }
    }

    return ['success' => false, 'error' => implode('; ', $errors)];
}

/**
 * 下载版本信息文件（多种方式尝试）
 * @return bool 是否成功
 */
function downloadVersionFile() {
    $versionUrl = 'https://d.ly-y.cn/up/new.txt';
    $tempDir = './temp';
    $versionFilePath = $tempDir . '/new.txt';

    // 确保临时目录存在
    if (!is_dir($tempDir)) {
        if (!mkdir($tempDir, 0755, true)) {
            return false;
        }
    }

    // 尝试多种下载方式
    $downloadMethods = [
        'curl',
        'file_get_contents',
        'wget',
        'fopen'
    ];

    $lastError = '';
    foreach ($downloadMethods as $method) {
        try {
            if (downloadWithMethod($versionUrl, $versionFilePath, $method)) {
                // 验证下载的文件内容
                if (file_exists($versionFilePath) && filesize($versionFilePath) > 0) {
                    $content = file_get_contents($versionFilePath);
                    if (!empty($content) && strpos($content, 'New_version=') !== false) {
                        return true;
                    }
                }
            }
        } catch (Exception $e) {
            $lastError = $e->getMessage();
        }
    }

    // 记录错误日志
    error_log("版本文件下载失败，最后错误: $lastError");
    return false;
}

/**
 * 使用指定方法下载文件（带详细错误信息）
 * @param string $url 下载链接
 * @param string $filePath 保存路径
 * @param string $method 下载方法
 * @return array 包含success和error字段的结果
 */
function downloadWithMethodDetailed($url, $filePath, $method) {
    try {
        switch ($method) {
            case 'curl':
                return downloadWithCurlDetailed($url, $filePath);
            case 'file_get_contents':
                return downloadWithFileGetContentsDetailed($url, $filePath);
            case 'wget':
                return downloadWithWgetDetailed($url, $filePath);
            case 'fopen':
                return downloadWithFopenDetailed($url, $filePath);
            default:
                return ['success' => false, 'error' => '未知的下载方法'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 使用指定方法下载文件
 * @param string $url 下载链接
 * @param string $filePath 保存路径
 * @param string $method 下载方法
 * @return bool 是否成功
 */
function downloadWithMethod($url, $filePath, $method) {
    $result = downloadWithMethodDetailed($url, $filePath, $method);
    return $result['success'];
}

/**
 * 使用cURL下载（带详细错误信息）
 */
function downloadWithCurlDetailed($url, $filePath) {
    if (!function_exists('curl_init')) {
        return ['success' => false, 'error' => 'cURL扩展不可用'];
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Template Updater/1.0');

    $data = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($data === false) {
        return ['success' => false, 'error' => 'cURL执行失败: ' . $error];
    }

    if ($httpCode !== 200) {
        return ['success' => false, 'error' => "HTTP错误码: $httpCode"];
    }

    if (!empty($error)) {
        return ['success' => false, 'error' => 'cURL错误: ' . $error];
    }

    if (file_put_contents($filePath, $data) === false) {
        return ['success' => false, 'error' => '无法写入文件'];
    }

    return ['success' => true];
}

/**
 * 使用cURL下载
 */
function downloadWithCurl($url, $filePath) {
    $result = downloadWithCurlDetailed($url, $filePath);
    return $result['success'];
}

/**
 * 使用file_get_contents下载（带详细错误信息）
 */
function downloadWithFileGetContentsDetailed($url, $filePath) {
    if (!ini_get('allow_url_fopen')) {
        return ['success' => false, 'error' => 'allow_url_fopen被禁用'];
    }

    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Template Updater/1.0',
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);

    $data = @file_get_contents($url, false, $context);

    if ($data === false) {
        $error = error_get_last();
        return ['success' => false, 'error' => 'file_get_contents失败: ' . ($error['message'] ?? '未知错误')];
    }

    if (empty($data)) {
        return ['success' => false, 'error' => '下载的数据为空'];
    }

    if (file_put_contents($filePath, $data) === false) {
        return ['success' => false, 'error' => '无法写入文件'];
    }

    return ['success' => true];
}

/**
 * 使用file_get_contents下载
 */
function downloadWithFileGetContents($url, $filePath) {
    $result = downloadWithFileGetContentsDetailed($url, $filePath);
    return $result['success'];
}

/**
 * 使用wget下载（带详细错误信息）
 */
function downloadWithWgetDetailed($url, $filePath) {
    $wgetPath = findWgetPath();
    if (!$wgetPath) {
        return ['success' => false, 'error' => 'wget命令不可用'];
    }

    $escapedUrl = escapeshellarg($url);
    $escapedFilePath = escapeshellarg($filePath);
    $command = "$wgetPath --timeout=30 --tries=3 --user-agent=\"Template Updater/1.0\" -O $escapedFilePath $escapedUrl 2>&1";

    $output = [];
    $exitCode = 0;
    exec($command, $output, $exitCode);

    if ($exitCode !== 0) {
        return ['success' => false, 'error' => "wget退出码: $exitCode, 输出: " . implode(' ', $output)];
    }

    if (!file_exists($filePath)) {
        return ['success' => false, 'error' => '文件未创建'];
    }

    if (filesize($filePath) === 0) {
        return ['success' => false, 'error' => '下载的文件为空'];
    }

    return ['success' => true];
}

/**
 * 使用wget下载
 */
function downloadWithWget($url, $filePath) {
    $result = downloadWithWgetDetailed($url, $filePath);
    return $result['success'];
}

/**
 * 使用fopen下载（带详细错误信息）
 */
function downloadWithFopenDetailed($url, $filePath) {
    if (!ini_get('allow_url_fopen')) {
        return ['success' => false, 'error' => 'allow_url_fopen被禁用'];
    }

    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Template Updater/1.0'
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);

    $source = @fopen($url, 'r', false, $context);
    if (!$source) {
        $error = error_get_last();
        return ['success' => false, 'error' => '无法打开源URL: ' . ($error['message'] ?? '未知错误')];
    }

    $dest = @fopen($filePath, 'w');
    if (!$dest) {
        fclose($source);
        $error = error_get_last();
        return ['success' => false, 'error' => '无法创建目标文件: ' . ($error['message'] ?? '未知错误')];
    }

    $bytes = stream_copy_to_stream($source, $dest);

    fclose($source);
    fclose($dest);

    if ($bytes === false) {
        return ['success' => false, 'error' => '数据复制失败'];
    }

    if (!file_exists($filePath)) {
        return ['success' => false, 'error' => '文件未创建'];
    }

    if (filesize($filePath) === 0) {
        return ['success' => false, 'error' => '下载的文件为空'];
    }

    return ['success' => true];
}

/**
 * 使用fopen下载
 */
function downloadWithFopen($url, $filePath) {
    $result = downloadWithFopenDetailed($url, $filePath);
    return $result['success'];
}

/**
 * 自动更新版本文件（每分钟调用一次）
 * @return bool 是否成功
 */
function autoUpdateVersionFile() {
    $tempDir = './temp';
    $versionFilePath = $tempDir . '/new.txt';
    $lockFilePath = $tempDir . '/update.lock';

    // 检查锁文件，避免重复执行
    if (file_exists($lockFilePath)) {
        $lockTime = filemtime($lockFilePath);
        // 如果锁文件存在且不超过2分钟，跳过更新
        if (time() - $lockTime < 120) {
            return true;
        }
    }

    // 创建锁文件
    file_put_contents($lockFilePath, time());

    // 检查是否需要更新（距离上次更新超过1分钟）
    if (file_exists($versionFilePath)) {
        $lastModified = filemtime($versionFilePath);
        if (time() - $lastModified < 60) {
            // 删除锁文件
            unlink($lockFilePath);
            return true; // 不需要更新
        }
    }

    // 下载最新版本文件
    $result = downloadVersionFile();

    // 删除锁文件
    if (file_exists($lockFilePath)) {
        unlink($lockFilePath);
    }

    return $result;
}

/**
 * 解析版本信息文件
 * @param string $filePath 版本文件路径
 * @return array|false 解析后的版本信息或false
 */
function parseVersionFile($filePath) {
    if (!file_exists($filePath)) {
        return false;
    }

    $content = file_get_contents($filePath);
    if ($content === false) {
        return false;
    }

    $lines = explode("\n", trim($content));
    $versionInfo = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) {
            continue;
        }

        // 解析 Version_push->1.0.0-2.7.1 格式
        if (strpos($line, 'Version_push->') === 0) {
            $versionInfo['Version_push'] = substr($line, strlen('Version_push->'));
        }
        // 解析 Download->url 格式（支持大小写）
        elseif (strpos($line, 'Download->') === 0) {
            $versionInfo['Download'] = substr($line, strlen('Download->'));
        }
        elseif (strpos($line, 'download->') === 0) {
            $versionInfo['Download'] = substr($line, strlen('download->'));
        }
        // 解析 New_version=2.7.2 格式
        elseif (strpos($line, 'New_version=') === 0) {
            $versionInfo['New_version'] = substr($line, strlen('New_version='));
        }
        // 解析 Now_version=2.7.1 格式
        elseif (strpos($line, 'Now_version=') === 0) {
            $versionInfo['Now_version'] = substr($line, strlen('Now_version='));
        }
        // 解析 Update_content=内容 格式
        elseif (strpos($line, 'Update_content=') === 0) {
            $content = substr($line, strlen('Update_content='));
            // 解码Unicode转义字符
            $versionInfo['Update_content'] = json_decode('"' . $content . '"');
        }
    }

    // 检查必要字段是否存在
    $requiredFields = ['Version_push', 'Download', 'New_version', 'Now_version', 'Update_content'];
    $missingFields = [];

    foreach ($requiredFields as $field) {
        if (!isset($versionInfo[$field])) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        // 记录调试信息
        error_log("版本文件解析失败，缺少字段: " . implode(', ', $missingFields));
        error_log("文件内容: " . $content);
        error_log("解析结果: " . print_r($versionInfo, true));
        return false;
    }

    return $versionInfo;
}

/**
 * 格式化字节数
 * @param int $bytes 字节数
 * @param int $precision 精度
 * @return string 格式化后的字符串
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * 解压ZIP文件
 * @param string $zipFile ZIP文件路径
 * @param string $extractPath 解压路径
 * @return bool 是否成功
 */
function extractZip($zipFile, $extractPath) {
    $zip = new ZipArchive;
    
    $openResult = $zip->open($zipFile);
    if ($openResult === true) {
        $extractResult = $zip->extractTo($extractPath);
        $zip->close();
        
        if ($extractResult === true) {
            return true;
        } else {
            // 解压失败
            echo "data: {\"error\": \"文件解压失败: 无法解压文件\"}\n\n";
            exit;
        }
    } else {
        // 打开ZIP文件失败
        $errorMsg = '未知错误';
        switch ($openResult) {
            case ZipArchive::ER_NOZIP:
                $errorMsg = '不是有效的ZIP文件';
                break;
            case ZipArchive::ER_INCONS:
                $errorMsg = 'ZIP文件不一致';
                break;
            case ZipArchive::ER_CRC:
                $errorMsg = 'ZIP文件CRC校验失败';
                break;
            case ZipArchive::ER_OPEN:
                $errorMsg = '无法打开ZIP文件';
                break;
        }
        echo "data: {\"error\": \"文件解压失败: " . $errorMsg . "\"}\n\n";
        exit;
    }
}
?>