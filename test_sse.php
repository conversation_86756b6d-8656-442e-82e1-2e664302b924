<!DOCTYPE html>
<html>
<head>
    <title>SSE测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Server-Sent Events 测试</h1>
    
    <button onclick="testSSE()">测试SSE连接</button>
    <button onclick="testDownload()">测试下载</button>
    
    <div id="output" style="margin-top: 20px; padding: 10px; background: #f0f0f0; white-space: pre-wrap; font-family: monospace;"></div>
    
    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }
        
        function testSSE() {
            log('开始测试SSE连接...');
            
            const testUrl = 'https://d.ly-y.cn/up/二五云魔方财务仿快云模板_v2.7.2.zip';
            const eventSource = new EventSource('update_handler.php?action=start_update&url=' + encodeURIComponent(testUrl));
            
            eventSource.onopen = function(event) {
                log('SSE连接已打开');
            };
            
            eventSource.onmessage = function(event) {
                log('收到消息: ' + event.data);
            };
            
            eventSource.onerror = function(event) {
                log('SSE错误: ' + JSON.stringify({
                    readyState: eventSource.readyState,
                    url: eventSource.url,
                    withCredentials: eventSource.withCredentials
                }));
                eventSource.close();
            };
            
            eventSource.addEventListener('complete', function(event) {
                log('完成事件: ' + event.data);
                eventSource.close();
            });
            
            eventSource.addEventListener('error', function(event) {
                log('错误事件: ' + event.data);
                eventSource.close();
            });
            
            // 10秒后自动关闭
            setTimeout(() => {
                if (eventSource.readyState !== EventSource.CLOSED) {
                    log('10秒超时，关闭连接');
                    eventSource.close();
                }
            }, 10000);
        }
        
        function testDownload() {
            log('开始测试普通下载...');
            
            fetch('update_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=check_update'
            })
            .then(response => response.json())
            .then(data => {
                log('检查更新结果: ' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                log('检查更新错误: ' + error.message);
            });
        }
    </script>
</body>
</html>
