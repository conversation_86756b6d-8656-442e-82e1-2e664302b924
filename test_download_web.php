<!DOCTYPE html>
<html>
<head>
    <title>下载测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>下载功能测试</h1>
    
    <?php
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    require_once 'update_handler.php';
    
    echo "<h2>PHP配置检查</h2>";
    echo "<p>allow_url_fopen: " . (ini_get('allow_url_fopen') ? '启用' : '禁用') . "</p>";
    echo "<p>cURL扩展: " . (function_exists('curl_init') ? '可用' : '不可用') . "</p>";
    echo "<p>OpenSSL: " . (extension_loaded('openssl') ? '可用' : '不可用') . "</p>";
    
    $testUrl = 'https://d.ly-y.cn/up/new.txt';
    echo "<h2>测试URL: $testUrl</h2>";
    
    // 确保temp目录存在
    if (!is_dir('./temp')) {
        mkdir('./temp', 0755, true);
    }
    
    // 测试各种下载方法
    $methods = [
        'curl' => 'cURL下载',
        'file_get_contents' => 'file_get_contents下载',
        'wget' => 'wget下载',
        'fopen' => 'fopen流下载'
    ];
    
    foreach ($methods as $method => $description) {
        echo "<h3>$description</h3>";
        
        $testFile = "./temp/test_$method.txt";
        
        // 删除之前的测试文件
        if (file_exists($testFile)) {
            unlink($testFile);
        }
        
        $result = downloadWithMethodDetailed($testUrl, $testFile, $method);
        
        if ($result['success']) {
            if (file_exists($testFile)) {
                $size = filesize($testFile);
                $content = file_get_contents($testFile);
                echo "<p style='color: green;'>✓ 成功 - 文件大小: {$size} 字节</p>";
                echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 200px; overflow: auto;'>" . htmlspecialchars($content) . "</pre>";
                
                // 验证内容格式
                if (strpos($content, 'New_version=') !== false) {
                    echo "<p style='color: green;'>✓ 内容格式正确</p>";
                } else {
                    echo "<p style='color: orange;'>⚠ 内容格式可能不正确</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ 文件不存在</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ 失败: " . htmlspecialchars($result['error']) . "</p>";
        }
        echo "<hr>";
    }
    
    // 测试综合下载函数
    echo "<h2>综合下载函数测试</h2>";
    $result = downloadVersionFileWithDetails();
    
    if ($result['success']) {
        echo "<p style='color: green;'>✓ 综合下载成功，使用方法: " . $result['method'] . "</p>";
        
        if (file_exists('./temp/new.txt')) {
            $content = file_get_contents('./temp/new.txt');
            echo "<h3>文件内容:</h3>";
            echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow: auto;'>" . htmlspecialchars($content) . "</pre>";
            
            // 解析版本信息
            $versionInfo = parseVersionFile('./temp/new.txt');
            if ($versionInfo) {
                echo "<h3>解析结果:</h3>";
                echo "<ul>";
                foreach ($versionInfo as $key => $value) {
                    echo "<li><strong>$key:</strong> " . htmlspecialchars($value) . "</li>";
                }
                echo "</ul>";
                
                $hasUpdate = ($versionInfo['New_version'] !== $versionInfo['Now_version']);
                echo "<p><strong>有新版本:</strong> " . ($hasUpdate ? '是' : '否') . "</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>✗ 综合下载失败: " . htmlspecialchars($result['error']) . "</p>";
    }
    
    // 测试网络连接
    echo "<h2>网络连接测试</h2>";
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $testUrl);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "<p>HTTP状态码: $httpCode</p>";
        if (!empty($error)) {
            echo "<p style='color: red;'>cURL错误: $error</p>";
        } else {
            echo "<p style='color: green;'>网络连接正常</p>";
        }
    } else {
        echo "<p style='color: red;'>cURL不可用，无法测试网络连接</p>";
    }
    ?>
    
</body>
</html>
