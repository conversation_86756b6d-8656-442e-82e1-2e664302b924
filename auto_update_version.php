<?php
/**
 * 自动更新版本文件脚本
 * 每分钟执行一次，从远程服务器下载最新的版本信息
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 生产环境关闭错误显示

// 包含更新处理函数
require_once 'update_handler.php';

// 记录日志函数
function writeLog($message) {
    $logFile = './temp/auto_update.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// 主执行逻辑
try {
    writeLog('开始自动更新版本文件检查');
    
    // 执行自动更新
    $result = autoUpdateVersionFile();
    
    if ($result) {
        writeLog('版本文件检查完成');
        
        // 检查是否有新版本
        $versionFilePath = './temp/new.txt';
        if (file_exists($versionFilePath)) {
            $versionInfo = parseVersionFile($versionFilePath);
            if ($versionInfo) {
                $hasUpdate = ($versionInfo['New_version'] !== $versionInfo['Now_version']);
                if ($hasUpdate) {
                    writeLog('发现新版本: ' . $versionInfo['New_version'] . ' (当前: ' . $versionInfo['Now_version'] . ')');
                } else {
                    writeLog('当前已是最新版本: ' . $versionInfo['Now_version']);
                }
            }
        }
    } else {
        writeLog('版本文件更新失败');
    }
    
} catch (Exception $e) {
    writeLog('自动更新出错: ' . $e->getMessage());
}

writeLog('自动更新检查结束');
?>
