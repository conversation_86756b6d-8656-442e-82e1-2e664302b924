<?php
// 配置文件

/**
 * 获取当前版本信息
 * @return string 当前版本号
 */
function getCurrentVersion() {
    $versionFile = './temp/new.txt';
    $defaultVersion = '2.7.1';

    // 如果版本文件不存在，尝试下载一次
    if (!file_exists($versionFile)) {
        // 尝试创建temp目录
        if (!is_dir('./temp')) {
            @mkdir('./temp', 0755, true);
        }

        // 尝试下载版本文件（静默执行，不影响页面加载）
        @downloadVersionFileQuiet();
    }

    // 再次检查文件是否存在
    if (!file_exists($versionFile)) {
        return $defaultVersion;
    }

    $content = file_get_contents($versionFile);
    if ($content === false) {
        return $defaultVersion;
    }

    $lines = explode("\n", trim($content));

    foreach ($lines as $line) {
        $line = trim($line);
        if (strpos($line, 'Now_version=') === 0) {
            $version = substr($line, strlen('Now_version='));
            return !empty($version) ? $version : $defaultVersion;
        }
    }

    return $defaultVersion;
}

/**
 * 静默下载版本文件（不输出错误）
 * @return bool 是否成功
 */
function downloadVersionFileQuiet() {
    $tempDir = './temp';
    $versionFilePath = $tempDir . '/new.txt';

    // 获取动态URL
    $versionUrl = getDynamicVersionUrl();

    // 确保临时目录存在
    if (!is_dir($tempDir)) {
        if (!@mkdir($tempDir, 0755, true)) {
            return false;
        }
    }

    // 尝试使用file_get_contents下载（简单快速）
    $content = @file_get_contents($versionUrl);
    if ($content !== false && !empty($content)) {
        return @file_put_contents($versionFilePath, $content) !== false;
    }

    return false;
}

/**
 * 获取动态版本检查URL
 * @return string 版本检查URL
 */
function getDynamicVersionUrl() {
    $versionFile = './temp/new.txt';
    $defaultUrl = 'https://d.ly-y.cn/up/new.txt';

    // 如果版本文件存在，尝试读取Now_user字段
    if (file_exists($versionFile)) {
        $content = @file_get_contents($versionFile);
        if ($content !== false) {
            $lines = explode("\n", trim($content));
            foreach ($lines as $line) {
                $line = trim($line);
                if (strpos($line, 'Now_user=') === 0) {
                    $url = substr($line, strlen('Now_user='));
                    return !empty($url) ? $url : $defaultUrl;
                }
            }
        }
    }

    return $defaultUrl;
}

// 当前模板版本（从版本文件实时读取）
$current_version = getCurrentVersion();

// 其他配置项可以在此添加
?>