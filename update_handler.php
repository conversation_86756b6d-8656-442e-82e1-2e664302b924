<?php
// 更新处理脚本

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

switch ($action) {
    case 'check_update':
        // 设置响应头
        header('Content-Type: application/json');
        
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        checkUpdate();
        break;
    case 'start_update':
        // 设置SSE响应头
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');
        
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        startUpdate();
        break;
    default:
        header('Content-Type: application/json');
        
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        echo json_encode(['error' => '无效的操作']);
        break;
}

/**
 * 检查更新
 */
function checkUpdate() {
    // 请求模板信息
    $templateUrl = 'https://25y.wujiyan.cc/template.json';
    $templateData = file_get_contents($templateUrl);
    
    if ($templateData === false) {
        $error = error_get_last();
        echo json_encode(['error' => '无法获取模板信息: ' . ($error['message'] ?? '未知错误')]);
        return;
    }
    
    $templateInfo = json_decode($templateData, true);
    
    if (!$templateInfo || !isset($templateInfo['version']) || !isset($templateInfo['url'])) {
        echo json_encode(['error' => '模板信息格式错误: ' . (json_last_error_msg() ?: '缺少必要字段')]);
        return;
    }
    
    echo json_encode([
        'version' => $templateInfo['version'],
        'url' => $templateInfo['url']
    ]);
}

/**
 * 开始更新
 */
function startUpdate() {
    // 获取下载链接
    $url = isset($_GET['url']) ? $_GET['url'] : (isset($_POST['url']) ? $_POST['url'] : '');
    
    if (empty($url)) {
        echo "data: {\"error\": \"缺少下载链接\"}\n\n";
        exit;
    }
    
    // 请求下载链接
    $downloadData = file_get_contents($url);
    
    if ($downloadData === false) {
        $error = error_get_last();
        echo "data: {\"error\": \"无法获取下载信息: " . ($error['message'] ?? '未知错误') . "\"}\n\n";
        exit;
    }
    
    $downloadInfo = json_decode($downloadData, true);
    
    if (!$downloadInfo || !isset($downloadInfo['code']) || $downloadInfo['code'] != 200) {
        echo "data: {\"error\": \"下载信息获取失败\"}\n\n";
        exit;
    }
    
    if (!isset($downloadInfo['downUrl'])) {
        echo "data: {\"error\": \"缺少下载地址\"}\n\n";
        exit;
    }
    
    $downUrl = $downloadInfo['downUrl'];
    $fileName = $downloadInfo['name'] ?? 'template.zip';
    
    // 创建临时目录
    $tempDir = './temp';
    if (!is_dir($tempDir)) {
        if (!mkdir($tempDir, 0755, true)) {
            $error = error_get_last();
            echo "data: {\"error\": \"无法创建临时目录: " . ($error['message'] ?? '未知错误') . "\"}\n\n";
            exit;
        }
    }
    
    // 检查目录是否可写
    if (!is_writable($tempDir)) {
        echo "data: {\"error\": \"临时目录不可写: " . $tempDir . "\"}\n\n";
        exit;
    }
    
    // 下载文件
    $filePath = $tempDir . '/' . $fileName;
    $downloadResult = downloadFile($downUrl, $filePath);
    
    if (!$downloadResult) {
        echo "data: {\"error\": \"文件下载失败\"}\n\n";
        exit;
    }
    
    // 解压文件
    $extractResult = extractZip($filePath, $tempDir);
    
    if (!$extractResult) {
        echo "data: {\"error\": \"文件解压失败\"}\n\n";
        exit;
    }
    
    // 发送完成事件
    echo "event: complete\n";
    echo "data: {\"success\": true, \"message\": \"更新完成\"}\n\n";
    exit;
}

/**
 * 下载文件
 * @param string $url 下载链接
 * @param string $filePath 保存路径
 * @return bool 是否成功
 */
function downloadFile($url, $filePath) {
    // 下载文件逻辑
    
    // 检查目录是否可写
    $dir = dirname($filePath);
    if (!is_writable($dir)) {
        echo "data: {\"error\": \"目录不可写: " . $dir . "\"}\n\n";
        exit;
    }
    
    $fp = fopen($filePath, 'w+');
    if ($fp === false) {
        $error = error_get_last();
        echo "data: {\"error\": \"无法创建文件: " . ($error['message'] ?? '未知错误') . "\"}\n\n";
        exit;
    }
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_FILE, $fp);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 300); // 5分钟超时
    curl_setopt($ch, CURLOPT_NOPROGRESS, false);
    curl_setopt($ch, CURLOPT_PROGRESSFUNCTION, function($resource, $downloadSize, $downloaded, $uploadSize, $uploaded) {
        if ($downloadSize > 0) {
            $progress = round(($downloaded / $downloadSize) * 100, 2);
            $speed = curl_getinfo($resource, CURLINFO_SPEED_DOWNLOAD);
            $speedFormatted = formatBytes($speed) . '/s';
            
            // 输出进度信息到前端
            echo "data: {\"progress\": \"$progress\", \"speed\": \"$speedFormatted\", \"downloaded\": \"" . formatBytes($downloaded) . "\", \"total\": \"" . formatBytes($downloadSize) . "\"}\n\n";
            ob_flush();
            flush();
        }
    });
    
    $result = curl_exec($ch);
    
    // 检查curl错误
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        fclose($fp);
        echo "data: {\"error\": \"下载文件时发生错误: " . $error . "\"}\n\n";
        exit;
    }
    
    curl_close($ch);
    fclose($fp);
    
    return $result !== false;
}

/**
 * 格式化字节数
 * @param int $bytes 字节数
 * @param int $precision 精度
 * @return string 格式化后的字符串
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * 解压ZIP文件
 * @param string $zipFile ZIP文件路径
 * @param string $extractPath 解压路径
 * @return bool 是否成功
 */
function extractZip($zipFile, $extractPath) {
    $zip = new ZipArchive;
    
    $openResult = $zip->open($zipFile);
    if ($openResult === true) {
        $extractResult = $zip->extractTo($extractPath);
        $zip->close();
        
        if ($extractResult === true) {
            return true;
        } else {
            // 解压失败
            echo "data: {\"error\": \"文件解压失败: 无法解压文件\"}\n\n";
            exit;
        }
    } else {
        // 打开ZIP文件失败
        $errorMsg = '未知错误';
        switch ($openResult) {
            case ZipArchive::ER_NOZIP:
                $errorMsg = '不是有效的ZIP文件';
                break;
            case ZipArchive::ER_INCONS:
                $errorMsg = 'ZIP文件不一致';
                break;
            case ZipArchive::ER_CRC:
                $errorMsg = 'ZIP文件CRC校验失败';
                break;
            case ZipArchive::ER_OPEN:
                $errorMsg = '无法打开ZIP文件';
                break;
        }
        echo "data: {\"error\": \"文件解压失败: " . $errorMsg . "\"}\n\n";
        exit;
    }
}
?>