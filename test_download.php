<?php
/**
 * 测试下载功能
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 测试下载功能 ===\n\n";

// 包含更新处理函数
require_once 'update_handler.php';

$testUrl = 'https://d.ly-y.cn/up/new.txt';
$testFile = './temp/test_download.txt';

// 确保temp目录存在
if (!is_dir('./temp')) {
    mkdir('./temp', 0755, true);
}

echo "测试URL: $testUrl\n";
echo "保存路径: $testFile\n\n";

// 测试各种下载方法
$methods = ['curl', 'file_get_contents', 'wget', 'fopen'];

foreach ($methods as $method) {
    echo "测试方法: $method\n";
    
    // 删除之前的测试文件
    if (file_exists($testFile)) {
        unlink($testFile);
    }
    
    $success = downloadWithMethod($testUrl, $testFile, $method);
    
    if ($success && file_exists($testFile)) {
        $size = filesize($testFile);
        $content = file_get_contents($testFile);
        echo "✓ 成功 - 文件大小: {$size} 字节\n";
        echo "内容预览: " . substr($content, 0, 100) . "...\n";
        
        // 验证内容格式
        if (strpos($content, 'New_version=') !== false) {
            echo "✓ 内容格式正确\n";
        } else {
            echo "✗ 内容格式错误\n";
        }
    } else {
        echo "✗ 失败\n";
    }
    echo "\n";
}

// 测试综合下载函数
echo "测试综合下载函数:\n";
if (file_exists($testFile)) {
    unlink($testFile);
}

$success = downloadVersionFile();
if ($success && file_exists('./temp/new.txt')) {
    $content = file_get_contents('./temp/new.txt');
    echo "✓ 综合下载成功\n";
    echo "文件内容:\n" . $content . "\n";
} else {
    echo "✗ 综合下载失败\n";
}

// 测试网络连接
echo "\n测试网络连接:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
if (!empty($error)) {
    echo "cURL错误: $error\n";
}

// 测试PHP配置
echo "\nPHP配置检查:\n";
echo "allow_url_fopen: " . (ini_get('allow_url_fopen') ? '启用' : '禁用') . "\n";
echo "cURL扩展: " . (function_exists('curl_init') ? '可用' : '不可用') . "\n";
echo "OpenSSL: " . (extension_loaded('openssl') ? '可用' : '不可用') . "\n";

echo "\n=== 测试完成 ===\n";
?>
